<template>
  <div class="client-dashboard">
    <!-- Background -->
    <div class="background-container">
      <div class="background-image"></div>
      <div class="background-overlay"></div>
    </div>

    <!-- Client Header with Navigation -->
    <ClientHeader
      :userName="userName"
      :userEmail="userEmail"
      :userAvatar="userAvatar"
      :showUserDropdown="showUserDropdown"
      :showBreadcrumbs="true"
      @user-dropdown-toggle="handleUserDropdownToggle"
      @menu-action="handleMenuAction"
      @logout="handleLogout"
      @error="handleError"
      @search="handleSearch"
    />

    <!-- Main Content -->
    <main id="main-content" class="main-content">
      <!-- Hero Section -->
      <HeroSection
        :firstName="firstName"
        :totalRequests="totalRequests"
        :pendingRequests="pendingRequests"
        @start-new-request="scrollToServices"
        @view-requests="goToMyRequests"
      />

      <!-- Quick Actions Section -->
      <QuickActionsSection
        @start-new-request="scrollToServices"
        @view-requests="goToMyRequests"
        @view-documents="goToMyDocuments"
        @get-help="openHelp"
      />

      <!-- Document Services Section -->
      <DocumentServicesSection
        ref="servicesSection"
        :documentTypes="documentTypes"
        :loading="loading"
        :error="error"
        @select-document-type="selectDocumentType"
        @retry="loadDocumentTypes"
      />

      <!-- Information and Help Section -->
      <InformationSection
        @open-help="openHelp"
        @contact-support="contactSupport"
      />
    </main>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import documentRequestService from '@/services/documentRequestService';
import ClientHeader from './ClientHeader.vue';
import HeroSection from './sections/HeroSection.vue';
import QuickActionsSection from './sections/QuickActionsSection.vue';
import DocumentServicesSection from './sections/DocumentServicesSection.vue';
import InformationSection from './sections/InformationSection.vue';
import unifiedAuthService from '@/services/unifiedAuthService';

export default {
  name: 'NewDocumentRequest',
  components: {
    ClientHeader,
    HeroSection,
    QuickActionsSection,
    DocumentServicesSection,
    InformationSection
  },
  setup() {
    // Reactive state
    const documentTypes = ref([]);
    const loading = ref(true);
    const error = ref(null);

    // Header state
    const showUserDropdown = ref(false);

    // User data
    const userName = ref('User');
    const userEmail = ref('<EMAIL>');
    const userAvatar = ref(null);
    const firstName = ref('User');
    const totalRequests = ref(0);
    const pendingRequests = ref(0);

    // Template refs
    const servicesSection = ref(null);
    // Methods
    const loadUserData = async () => {
      try {
        const currentUser = unifiedAuthService.getCurrentUser();
        if (currentUser) {
          userName.value = currentUser.username || 'User';
          userEmail.value = currentUser.email || '<EMAIL>';
          firstName.value = currentUser.first_name || currentUser.username || 'User';
          userAvatar.value = currentUser.avatar || null;
        }
      } catch (error) {
        console.error('Error loading user data:', error);
      }
    };

    const loadUserStats = async () => {
      try {
        // TODO: Implement API call to get user statistics
        // For now, using placeholder data
        totalRequests.value = 5;
        pendingRequests.value = 2;
      } catch (error) {
        console.error('Error loading user stats:', error);
      }
    };

    const loadDocumentTypes = async () => {
      try {
        loading.value = true;
        error.value = null;

        const response = await documentRequestService.getDocumentTypes();
        documentTypes.value = response.data || [];

      } catch (err) {
        console.error('Error loading document types:', err);
        error.value = err.response?.data?.message || 'Failed to load available services';
      } finally {
        loading.value = false;
      }
    };

    const selectDocumentType = (documentType) => {
      if (!documentType.is_active) return;

      const routeName = getRouteForDocumentType(documentType.type_name);
      if (routeName) {
        console.log('Navigate to:', routeName, 'with ID:', documentType.id);
      }
    };

    const getRouteForDocumentType = (typeName) => {
      const routes = {
        'Barangay Clearance': 'BarangayClearanceRequest',
        'Cedula': 'CedulaRequest'
      };
      return routes[typeName];
    };

    // Header event handlers
    const handleUserDropdownToggle = () => {
      showUserDropdown.value = !showUserDropdown.value;
    };

    const handleMenuAction = (action) => {
      console.log('Menu action:', action);
    };

    const handleLogout = () => {
      try {
        unifiedAuthService.logout();
        console.log('Logout and navigate to WelcomePage');
      } catch (err) {
        console.error('Logout error:', err);
      }
    };

    const handleError = (err) => {
      console.error('Header error:', err);
    };

    const handleSearch = (query) => {
      console.log('Search query:', query);
    };

    // Navigation methods
    const scrollToServices = () => {
      servicesSection.value?.$el?.scrollIntoView({ behavior: 'smooth' });
    };

    const goToMyRequests = () => {
      console.log('Navigate to MyRequests');
    };

    const goToMyDocuments = () => {
      console.log('Navigate to MyDocuments');
    };

    const openHelp = () => {
      console.log('Opening help...');
    };

    const contactSupport = () => {
      console.log('Contacting support...');
    };

    // Lifecycle
    onMounted(async () => {
      await loadUserData();
      await loadDocumentTypes();
      await loadUserStats();
    });

    // Return reactive state and methods for template
    return {
      // State
      documentTypes,
      loading,
      error,
      showUserDropdown,
      userName,
      userEmail,
      userAvatar,
      firstName,
      totalRequests,
      pendingRequests,
      servicesSection,

      // Methods
      selectDocumentType,
      loadDocumentTypes,
      handleUserDropdownToggle,
      handleMenuAction,
      handleLogout,
      handleError,
      handleSearch,
      scrollToServices,
      goToMyRequests,
      goToMyDocuments,
      openHelp,
      contactSupport
    };
  }
};
</script>

<style scoped>
/* CSS Variables */
:root {
  --spacing-16: 4rem;
}

/* Client Dashboard Layout */
.client-dashboard {
  min-height: 100vh;
  background: #f9f9f9;
  position: relative;
}

/* Background Setup */
.background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.background-image {
  width: 100%;
  height: 100%;
  background-image: url('@/assets/bula-request-background-pic.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  opacity: 0.03;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 94, 162, 0.02) 0%,
    rgba(35, 120, 195, 0.03) 100%
  );
  z-index: -1;
}

/* Main Content */
.main-content {
  margin-top: 140px; /* Account for fixed header */
  padding-bottom: var(--spacing-16);
  position: relative;
  z-index: 1;
}
</style>
