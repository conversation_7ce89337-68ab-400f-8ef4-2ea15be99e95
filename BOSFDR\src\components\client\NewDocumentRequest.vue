<template>
  <div class="client-dashboard">
    <!-- Background -->
    <div class="background-container">
      <div class="background-image"></div>
      <div class="background-overlay"></div>
    </div>

    <!-- Client Header with Navigation -->
    <ClientHeader
      :userName="userName"
      :userEmail="userEmail"
      :userAvatar="userAvatar"
      :showUserDropdown="showUserDropdown"
      :sidebarCollapsed="sidebarCollapsed"
      :activeMenu="activeMenu"
      :showBreadcrumbs="true"
      @sidebar-toggle="handleSidebarToggle"
      @user-dropdown-toggle="handleUserDropdownToggle"
      @menu-action="handleMenuAction"
      @logout="handleLogout"
      @error="handleError"
      @search="handleSearch"
    />

    <!-- Main Content -->
    <main id="main-content" class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- Hero Section -->
      <HeroSection
        :firstName="firstName"
        :totalRequests="totalRequests"
        :pendingRequests="pendingRequests"
        @start-new-request="scrollToServices"
        @view-requests="goToMyRequests"
      />

      <!-- Quick Actions Section -->
      <QuickActionsSection
        @start-new-request="scrollToServices"
        @view-requests="goToMyRequests"
        @view-documents="goToMyDocuments"
        @get-help="openHelp"
      />

      <!-- Document Services Section -->
      <DocumentServicesSection
        ref="servicesSection"
        :documentTypes="documentTypes"
        :loading="loading"
        :error="error"
        @select-document-type="selectDocumentType"
        @retry="loadDocumentTypes"
      />

      <!-- Information and Help Section -->
      <InformationSection
        @open-help="openHelp"
        @contact-support="contactSupport"
      />
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import documentRequestService from '@/services/documentRequestService';
import ClientHeader from './ClientHeader.vue';
import HeroSection from './sections/HeroSection.vue';
import QuickActionsSection from './sections/QuickActionsSection.vue';
import DocumentServicesSection from './sections/DocumentServicesSection.vue';
import InformationSection from './sections/InformationSection.vue';
import unifiedAuthService from '@/services/unifiedAuthService';

export default {
  name: 'NewDocumentRequest',
  components: {
    ClientHeader,
    HeroSection,
    QuickActionsSection,
    DocumentServicesSection,
    InformationSection
  },
  setup() {
    // Reactive state
    const documentTypes = ref([]);
    const loading = ref(true);
    const error = ref(null);

    // Header state
    const showUserDropdown = ref(false);
    const sidebarCollapsed = ref(false);
    const activeMenu = ref('dashboard');

    // User data
    const userName = ref('User');
    const userEmail = ref('<EMAIL>');
    const userAvatar = ref(null);
    const firstName = ref('User');
    const totalRequests = ref(0);
    const pendingRequests = ref(0);

    // Template refs
    const servicesSection = ref(null);
    // Methods
    const loadUserData = async () => {
      try {
        const currentUser = unifiedAuthService.getCurrentUser();
        if (currentUser) {
          userName.value = currentUser.username || 'User';
          userEmail.value = currentUser.email || '<EMAIL>';
          firstName.value = currentUser.first_name || currentUser.username || 'User';
          userAvatar.value = currentUser.avatar || null;
        }
      } catch (error) {
        console.error('Error loading user data:', error);
      }
    };

    const loadUserStats = async () => {
      try {
        // TODO: Implement API call to get user statistics
        // For now, using placeholder data
        totalRequests.value = 5;
        pendingRequests.value = 2;
      } catch (error) {
        console.error('Error loading user stats:', error);
      }
    };

    const loadDocumentTypes = async () => {
      try {
        loading.value = true;
        error.value = null;

        const response = await documentRequestService.getDocumentTypes();
        documentTypes.value = response.data || [];

      } catch (err) {
        console.error('Error loading document types:', err);
        error.value = err.response?.data?.message || 'Failed to load available services';
      } finally {
        loading.value = false;
      }
    };

    // Header event handlers
    handleSidebarToggle() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    },

    handleUserDropdownToggle() {
      this.showUserDropdown = !this.showUserDropdown;
    },

    handleMenuAction(action) {
      console.log('Menu action:', action);
      switch (action) {
        case 'profile':
          // TODO: Navigate to profile page
          break;
        case 'settings':
          // TODO: Navigate to settings page
          break;
        case 'account':
          // TODO: Navigate to account page
          break;
      }
    },

    handleLogout() {
      try {
        unifiedAuthService.logout();
        this.$router.push({ name: 'WelcomePage' });
      } catch (error) {
        console.error('Logout error:', error);
      }
    },

    handleError(error) {
      console.error('Header error:', error);
    },

    // Search handler
    handleSearch(query) {
      console.log('Search query:', query);
      // TODO: Implement search functionality
      // This could search through documents, services, or requests
      // For now, we'll just log the query
    },

    // Navigation methods
    scrollToServices() {
      this.$refs.servicesSection?.scrollIntoView({ behavior: 'smooth' });
    },

    goToMyRequests() {
      this.$router.push({ name: 'MyRequests' });
    },

    goToProfile() {
      // TODO: Navigate to profile page
      console.log('Navigate to profile');
    },

    goBack() {
      this.$router.push({ name: 'ClientDashboard' });
    },

    openHelp() {
      // TODO: Implement help/FAQ modal or page
      console.log('Opening help...');
    },

    contactSupport() {
      // TODO: Implement contact support functionality
      console.log('Contacting support...');
      // Could open a modal, redirect to contact page, or open email client
      // For now, we'll just log the action
    }
  }
};
</script>

<style scoped>
/* Modern Government Portal Styles - USWDS Inspired */
:root {
  /* Government Colors */
  --gov-blue: #005ea2;
  --gov-blue-dark: #0f4c96;
  --gov-blue-light: #2378c3;
  --gov-blue-lighter: #e7f6f8;
  --gov-red: #d63384;
  --gov-green: #00a91c;
  --gov-yellow: #ffbe2e;
  --gov-yellow-light: #fef0cd;

  /* Neutral Colors */
  --text-primary: #1b1b1b;
  --text-secondary: #454545;
  --text-light: #757575;
  --text-white: #ffffff;

  /* Background Colors */
  --bg-white: #ffffff;
  --bg-gray-5: #f9f9f9;
  --bg-gray-10: #f0f0f0;
  --bg-gray-20: #dfe1e2;

  /* Shadows */
  --shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  --shadow-2: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
  --shadow-3: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
  --shadow-4: 0 8px 16px 0 rgba(0, 0, 0, 0.1);

  /* Typography */
  --font-family-sans: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-serif: 'Merriweather', Georgia, serif;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;

  /* Border Radius */
  --border-radius-sm: 0.125rem;
  --border-radius-md: 0.25rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-base: 0.2s ease-in-out;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
}

.client-dashboard {
  font-family: var(--font-family-sans);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-gray-5);
  min-height: 100vh;
}

/* Background Setup */
.background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.background-image {
  width: 100%;
  height: 100%;
  background-image: url('@/assets/bula-request-background-pic.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  opacity: 0.03;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 94, 162, 0.02) 0%,
    rgba(35, 120, 195, 0.03) 100%
  );
  z-index: -1;
}

/* Container utility */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* Main Content */
.main-content {
  margin-top: 140px; /* Account for new fixed header (gov banner + main header + breadcrumb) */
  transition: margin-left var(--transition-base);
  padding-bottom: var(--spacing-16);
}

.main-content.sidebar-collapsed {
  margin-left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
    padding: var(--spacing-6);
  }

  .hero-title {
    font-size: clamp(1.5rem, 6vw, 2rem);
  }

  .hero-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-2);
  }

  .stat-card {
    padding: var(--spacing-3);
    min-height: 100px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  .action-card {
    padding: var(--spacing-4);
    gap: var(--spacing-3);
  }

  .action-icon {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
  }

  .hero-section,
  .quick-actions-section {
    margin: var(--spacing-2);
  }
}

/* Modern Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  font-family: var(--font-family-sans);
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.2;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--gov-yellow);
  outline-offset: 2px;
}

.btn-primary {
  background-color: var(--gov-blue);
  color: var(--text-white);
  border-color: var(--gov-blue);
}

.btn-primary:hover {
  background-color: var(--gov-blue-dark);
  border-color: var(--gov-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-3);
}

.btn-secondary {
  background-color: var(--bg-white);
  color: var(--gov-blue);
  border-color: var(--gov-blue);
}

.btn-secondary:hover {
  background-color: var(--gov-blue);
  color: var(--text-white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-3);
}

.btn-outline {
  background-color: transparent;
  color: var(--gov-blue);
  border-color: var(--gov-blue);
}

.btn-outline:hover {
  background-color: var(--gov-blue);
  color: var(--text-white);
}

/* Hero Section */
.hero-section {
  padding: var(--spacing-8) 0;
  position: relative;
  z-index: 1;
  background: var(--bg-white);
  margin: var(--spacing-4);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-2);
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--spacing-8);
  align-items: start;
  padding: var(--spacing-8);
}

.hero-text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.hero-title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: var(--gov-blue);
  margin: 0;
  line-height: 1.2;
  font-family: var(--font-family-serif);
}

.hero-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
  max-width: 600px;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
  margin-top: var(--spacing-2);
}

.hero-stats {
  display: flex;
  justify-content: center;
  min-width: 280px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-3);
  width: 100%;
}

.stat-card {
  background: linear-gradient(135deg, var(--gov-blue), var(--gov-blue-light));
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  color: var(--text-white);
  text-align: center;
  flex: 1;
  box-shadow: var(--shadow-2);
  transition: all var(--transition-fast);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-3);
}

.stat-icon {
  font-size: 1.5rem;
  color: var(--gov-yellow);
  margin-bottom: var(--spacing-2);
  display: block;
}

.stat-number {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: var(--spacing-1);
  color: var(--gov-yellow);
  font-family: var(--font-family-serif);
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.9;
  font-weight: 500;
  line-height: 1.2;
}

/* Quick Actions Section */
.quick-actions-section {
  padding: var(--spacing-8) 0;
  position: relative;
  z-index: 1;
  background: var(--bg-gray-5);
  margin: var(--spacing-4);
  border-radius: var(--border-radius-xl);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
  padding: 0 var(--spacing-4);
}

.section-title {
  font-size: clamp(1.75rem, 4vw, 2.25rem);
  font-weight: 700;
  color: var(--gov-blue);
  margin-bottom: var(--spacing-3);
  font-family: var(--font-family-serif);
}

.section-description {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: var(--spacing-4);
  padding: 0 var(--spacing-4);
}

.action-card {
  background: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 2px solid var(--bg-gray-20);
  box-shadow: var(--shadow-1);
}

.action-card:hover,
.action-card:focus {
  transform: translateY(-2px);
  box-shadow: var(--shadow-3);
  border-color: var(--gov-blue);
  outline: none;
}

.action-card.primary {
  background: linear-gradient(135deg, var(--gov-blue), var(--gov-blue-light));
  color: var(--text-white);
  border-color: var(--gov-blue);
}

.action-card.primary:hover,
.action-card.primary:focus {
  border-color: var(--gov-yellow);
  box-shadow: var(--shadow-4);
}

.action-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #1e3a8a;
  flex-shrink: 0;
}

.action-card.primary .action-icon {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.action-content {
  flex: 1;
}

.action-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: inherit;
}

.action-content p {
  margin: 0;
  opacity: 0.8;
  line-height: 1.5;
}

.action-arrow {
  color: #6b7280;
  font-size: 1.25rem;
}

.action-card.primary .action-arrow {
  color: rgba(255, 255, 255, 0.8);
}

/* Services Section */
.services-section {
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.services-section .section-title {
  color: white;
}

.services-section .section-description {
  color: rgba(255, 255, 255, 0.9);
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-spinner i {
  font-size: 3rem;
  color: #1e3a8a;
  margin-bottom: 1rem;
}

.error-content {
  max-width: 400px;
}

.error-content i {
  font-size: 3rem;
  color: #dc2626;
  margin-bottom: 1rem;
}

.retry-btn {
  background: linear-gradient(135deg, #1e3a8a, #1e40af);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
}

.document-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.document-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(30, 58, 138, 0.2);
  border-radius: 1rem;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.document-card:hover:not(.disabled) {
  border-color: #1e3a8a;
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(30, 58, 138, 0.2);
  background: rgba(255, 255, 255, 1);
}

.document-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.document-icon {
  flex-shrink: 0;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #1e3a8a, #1e40af);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.document-content {
  flex: 1;
}

.document-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e3a8a;
  margin-bottom: 0.5rem;
}

.document-description {
  color: #6b7280;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.document-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.fee-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.fee-label {
  color: #9ca3af;
  font-size: 0.9rem;
}

.fee-amount {
  font-weight: 600;
  color: #059669;
  font-size: 1.1rem;
}

.processing-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.9rem;
}

.document-action {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.document-action i {
  color: #d1d5db;
  font-size: 1.25rem;
}

.status-badge.unavailable {
  background: #fecaca;
  color: #dc2626;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Info Section */
.info-section {
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.info-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.info-card, .help-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(30, 58, 138, 0.2);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.info-card:hover, .help-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.15);
  background: rgba(255, 255, 255, 1);
  border-color: #1e3a8a;
}

.info-header, .help-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.info-header h3, .help-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e3a8a;
  margin: 0;
}

.info-header i {
  color: #1e3a8a;
  font-size: 1.25rem;
}

.help-header i {
  color: #059669;
  font-size: 1.25rem;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-list li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: #6b7280;
  line-height: 1.6;
}

.info-list i {
  color: #059669;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.help-content p {
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.help-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.help-btn, .contact-btn {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  border: 2px solid rgba(30, 58, 138, 0.3);
  padding: 0.875rem 1.25rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  font-size: 0.95rem;
}

.help-btn:hover {
  border-color: #1e3a8a;
  color: #1e3a8a;
  background: rgba(30, 58, 138, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);
}

.contact-btn:hover {
  border-color: #059669;
  color: #059669;
  background: rgba(5, 150, 105, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 0.75rem;
  }

  .welcome-header {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .welcome-stats {
    justify-content: center;
  }

  .stat-card {
    min-width: 120px;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .action-card {
    padding: 1.5rem;
  }

  .document-types-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .document-card {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .info-card, .help-card {
    padding: 1.5rem;
  }

  .loading-container, .error-container {
    padding: 3rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }

  .welcome-section,
  .quick-actions-section,
  .services-section,
  .info-section {
    padding: 1.5rem 0;
  }

  .welcome-header {
    padding: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .action-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .action-icon {
    width: 3rem;
    height: 3rem;
  }

  .document-card {
    padding: 1rem;
  }

  .info-card, .help-card {
    padding: 1rem;
  }

  .loading-container, .error-container {
    padding: 2rem 1rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-section,
.quick-actions-section,
.services-section,
.info-section {
  animation: fadeInUp 0.8s ease-out;
}

.quick-actions-section {
  animation-delay: 0.2s;
}

.services-section {
  animation-delay: 0.4s;
}

.info-section {
  animation-delay: 0.6s;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation: none !important;
    transition: none !important;
  }

  .action-card:hover,
  .document-card:hover,
  .info-card:hover,
  .help-card:hover {
    transform: none;
  }
}

/* Focus styles */
.action-card:focus,
.document-card:focus,
.help-btn:focus,
.contact-btn:focus,
.retry-btn:focus {
  outline: 3px solid #fbbf24;
  outline-offset: 2px;
}
</style>
