<template>
  <section class="hero-section" aria-labelledby="hero-title">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <h1 id="hero-title" class="hero-title">
            Welcome back, {{ firstName }}!
          </h1>
          <p class="hero-subtitle">
            Access government services and request official documents through our secure digital platform.
          </p>
          <div class="hero-actions">
            <button 
              class="btn btn-primary" 
              @click="$emit('start-new-request')"
              type="button"
            >
              <svg class="btn-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
              </svg>
              Start New Request
            </button>
            <button 
              class="btn btn-secondary" 
              @click="$emit('view-requests')"
              type="button"
            >
              <svg class="btn-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3,5H9V11H3V5M5,7V9H7V7H5M11,7H21V9H11V7M11,15H21V17H11V15M5,20L1.5,16.5L2.91,15.09L5,17.17L9.59,12.59L11,14L5,20Z"/>
              </svg>
              View My Requests
            </button>
          </div>
        </div>
        
        <div class="hero-stats">
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">
                <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ totalRequests }}</div>
                <div class="stat-label">Total Requests</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                </svg>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ pendingRequests }}</div>
                <div class="stat-label">Pending</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
                </svg>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ completedRequests }}</div>
                <div class="stat-label">Completed</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { computed } from 'vue';

export default {
  name: 'HeroSection',
  props: {
    firstName: {
      type: String,
      required: true
    },
    totalRequests: {
      type: Number,
      default: 0
    },
    pendingRequests: {
      type: Number,
      default: 0
    }
  },
  emits: [
    'start-new-request',
    'view-requests'
  ],
  setup(props) {
    const completedRequests = computed(() => {
      return Math.max(0, props.totalRequests - props.pendingRequests);
    });

    return {
      completedRequests
    };
  }
};
</script>

<style scoped>
/* Hero Section Styles */
.hero-section {
  padding: var(--spacing-12) 0 var(--spacing-10);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: var(--color-text-inverse);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/assets/images/gov-pattern.svg') repeat;
  opacity: 0.1;
  z-index: 1;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  position: relative;
  z-index: 2;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--spacing-10);
  align-items: center;
}

.hero-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  line-height: var(--line-height-2);
  margin: 0 0 var(--spacing-4);
  color: var(--color-text-inverse);
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-5);
  margin: 0 0 var(--spacing-8);
  opacity: 0.9;
  max-width: 600px;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-md);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-base) var(--easing-standard);
  min-height: 48px; /* WCAG touch target */
}

.btn-icon {
  width: 20px;
  height: 20px;
}

.btn-primary {
  background: var(--color-text-inverse);
  color: var(--color-primary);
  border-color: var(--color-text-inverse);
}

.btn-primary:hover,
.btn-primary:focus {
  background: var(--color-bg-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-3);
}

.btn-secondary {
  background: transparent;
  color: var(--color-text-inverse);
  border-color: var(--color-text-inverse);
}

.btn-secondary:hover,
.btn-secondary:focus {
  background: var(--color-text-inverse);
  color: var(--color-primary);
}

.btn:focus {
  outline: var(--focus-ring-width) solid var(--color-accent);
  outline-offset: var(--focus-ring-offset);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-4);
  min-width: 300px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--spacing-5);
  text-align: center;
  transition: transform var(--duration-base) var(--easing-standard);
}

.stat-card:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--spacing-3);
  color: var(--color-accent);
}

.stat-icon svg {
  width: 100%;
  height: 100%;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  line-height: var(--line-height-1);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.8;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    text-align: center;
  }
  
  .hero-title {
    font-size: var(--font-size-2xl);
  }
  
  .hero-actions {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }
}

@media (max-width: 480px) {
  .hero-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn {
    justify-content: center;
  }
}
</style>
