<template>
  <!-- Skip Navigation Link for Accessibility -->
  <a href="#main-content" class="skip-link">Skip to main content</a>

  <header class="client-header" role="banner" aria-label="Site header">
    <!-- Government Banner - USWDS Compliant -->
    <section
      class="gov-banner"
      aria-label="Official website of Barangay Bula, General Santos City"
    >
      <div class="usa-accordion">
        <header class="usa-banner__header">
          <div class="usa-banner__inner">
            <div class="grid-col-auto">
              <img
                aria-hidden="true"
                class="usa-banner__header-flag"
                src="/assets/images/ph_flag_small.png"
                alt=""
              />
            </div>
            <div class="grid-col-fill tablet:grid-col-auto" aria-hidden="true">
              <p class="usa-banner__header-text">
                An official website of Barangay Bula, General Santos City
              </p>
              <p class="usa-banner__header-action">Here's how you know</p>
            </div>
            <button
              type="button"
              class="usa-accordion__button usa-banner__button"
              :aria-expanded="showBannerDetails"
              aria-controls="gov-banner-content"
              @click="toggleBannerDetails"
            >
              <span class="usa-banner__button-text">Here's how you know</span>
            </button>
          </div>
        </header>
        <div
          class="usa-banner__content usa-accordion__content"
          id="gov-banner-content"
          :hidden="!showBannerDetails"
        >
          <div class="grid-row grid-gap-lg">
            <div class="usa-banner__guidance tablet:grid-col-6">
              <img
                class="usa-banner__icon usa-media-block__img"
                src="/assets/images/icon-dot-gov.svg"
                role="img"
                alt=""
                aria-hidden="true"
              />
              <div class="usa-media-block__body">
                <p>
                  <strong>Official websites use .gov.ph</strong><br />
                  A <strong>.gov.ph</strong> website belongs to an official government
                  organization in the Philippines.
                </p>
              </div>
            </div>
            <div class="usa-banner__guidance tablet:grid-col-6">
              <img
                class="usa-banner__icon usa-media-block__img"
                src="/assets/images/icon-https.svg"
                role="img"
                alt=""
                aria-hidden="true"
              />
              <div class="usa-media-block__body">
                <p>
                  <strong>Secure .gov.ph websites use HTTPS</strong><br />
                  A <strong>lock</strong> (🔒) or <strong>https://</strong> means you've
                  safely connected to the .gov.ph website. Share sensitive information
                  only on official, secure websites.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Main Header -->
    <div class="main-header">
      <div class="header-container">
        <!-- Left Section: Logo and Navigation -->
        <div class="header-left">
          <button
            class="logo-section"
            @click="handleMenuAction('dashboard')"
            :aria-label="`Go to ${getPageTitle()} dashboard`"
            type="button"
          >
            <img
              src="@/assets/icon-of-bula.jpg"
              alt="Barangay Bula Logo"
              class="logo"
              width="40"
              height="40"
            />
            <div class="site-identity">
              <h1 class="site-title">Barangay Bula</h1>
              <span class="site-subtitle">Digital Services Portal</span>
            </div>
          </button>

          <!-- Mobile Menu Toggle -->
          <button
            class="mobile-menu-toggle"
            @click="handleSidebarToggle"
            :aria-label="sidebarCollapsed ? 'Open navigation menu' : 'Close navigation menu'"
            :aria-expanded="!sidebarCollapsed"
            type="button"
          >
            <span class="hamburger-line" :class="{ active: !sidebarCollapsed }"></span>
            <span class="hamburger-line" :class="{ active: !sidebarCollapsed }"></span>
            <span class="hamburger-line" :class="{ active: !sidebarCollapsed }"></span>
          </button>
        </div>

        <!-- Center Section: Navigation (Desktop) -->
        <nav class="main-navigation" role="navigation" aria-label="Main navigation">
          <ul class="nav-list" role="menubar">
            <li class="nav-item" role="none">
              <button
                class="nav-link"
                :class="{ active: activeMenu === 'dashboard' }"
                @click="handleMenuAction('dashboard')"
                role="menuitem"
                :aria-current="activeMenu === 'dashboard' ? 'page' : undefined"
                type="button"
              >
                <svg class="nav-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                <span>Dashboard</span>
              </button>
            </li>
            <li class="nav-item" role="none">
              <button
                class="nav-link"
                :class="{ active: activeMenu === 'services' }"
                @click="handleMenuAction('services')"
                role="menuitem"
                :aria-current="activeMenu === 'services' ? 'page' : undefined"
                type="button"
              >
                <svg class="nav-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
                <span>Services</span>
              </button>
            </li>
            <li class="nav-item" role="none">
              <button
                class="nav-link"
                :class="{ active: activeMenu === 'requests' }"
                @click="handleMenuAction('requests')"
                role="menuitem"
                :aria-current="activeMenu === 'requests' ? 'page' : undefined"
                type="button"
              >
                <svg class="nav-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                </svg>
                <span>My Requests</span>
              </button>
            </li>
            <li class="nav-item" role="none">
              <button
                class="nav-link"
                :class="{ active: activeMenu === 'help' }"
                @click="handleMenuAction('help')"
                role="menuitem"
                :aria-current="activeMenu === 'help' ? 'page' : undefined"
                type="button"
              >
                <svg class="nav-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C7.59,8 4,8 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z"/>
                </svg>
                <span>Help</span>
              </button>
            </li>
          </ul>
        </nav>

        <!-- Right Section: User Actions -->
        <div class="header-actions">
          <!-- Search -->
          <div class="search-container">
            <button class="search-toggle" @click="toggleSearch" aria-label="Search documents and services" :aria-expanded="showSearch">
              <i class="fas fa-search" aria-hidden="true"></i>
            </button>
            <div class="search-box" :class="{ active: showSearch }" role="search">
              <label for="header-search" class="sr-only">Search documents and services</label>
              <input
                id="header-search"
                type="search"
                placeholder="Search documents, services..."
                class="search-input"
                v-model="searchQuery"
                @keyup.enter="performSearch"
                autocomplete="off"
              />
              <button class="search-submit" @click="performSearch" aria-label="Submit search">
                <i class="fas fa-search" aria-hidden="true"></i>
              </button>
            </div>
          </div>

          <!-- Notifications -->
          <ClientNotifications
            @new-notification="handleNewNotification"
            @notification-click="handleNotificationClick"
            @error="handleNotificationError"
          />

          <!-- User Profile -->
          <div class="user-profile" :class="{ active: showUserDropdown }">
            <button class="user-btn" @click="handleUserDropdownToggle" aria-label="User account menu" :aria-expanded="showUserDropdown">
              <div class="user-avatar">
                <img v-if="userAvatar" :src="userAvatar" :alt="userName" class="avatar-image" />
                <i v-else class="fas fa-user-circle avatar-icon" aria-hidden="true"></i>
              </div>
              <div class="user-info">
                <span class="user-name">{{ userName }}</span>
                <span class="user-role">Client Portal</span>
              </div>
              <i class="fas fa-chevron-down dropdown-arrow" aria-hidden="true"></i>
            </button>

            <div v-if="showUserDropdown" class="user-dropdown-menu" role="menu" aria-label="User account options">
              <div class="dropdown-header">
                <div class="user-details">
                  <strong>{{ userName }}</strong>
                  <span class="user-email">{{ userEmail }}</span>
                </div>
              </div>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item" @click="handleMenuAction('profile')" role="menuitem">
                <i class="fas fa-user" aria-hidden="true"></i>
                <span>My Profile</span>
              </a>
              <a href="#" class="dropdown-item" @click="handleMenuAction('settings')" role="menuitem">
                <i class="fas fa-cog" aria-hidden="true"></i>
                <span>Account Settings</span>
              </a>
              <a href="#" class="dropdown-item" @click="handleMenuAction('documents')" role="menuitem">
                <i class="fas fa-folder" aria-hidden="true"></i>
                <span>My Documents</span>
              </a>
              <a href="#" class="dropdown-item" @click="handleMenuAction('history')" role="menuitem">
                <i class="fas fa-history" aria-hidden="true"></i>
                <span>Request History</span>
              </a>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item" @click="handleMenuAction('help')" role="menuitem">
                <i class="fas fa-question-circle" aria-hidden="true"></i>
                <span>Help & Support</span>
              </a>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item logout-item" @click="handleLogout" role="menuitem">
                <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
                <span>Sign Out</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-section" v-if="showBreadcrumbs">
      <div class="header-container">
        <nav class="breadcrumb-nav" aria-label="Breadcrumb navigation">
          <ol class="breadcrumb-list">
            <li class="breadcrumb-item">
              <a href="#" @click="handleMenuAction('dashboard')">
                <i class="fas fa-home" aria-hidden="true"></i>
                Dashboard
              </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
              {{ getPageTitle() }}
            </li>
          </ol>
        </nav>
      </div>
    </div>
  </header>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';
import ClientNotifications from './ClientNotifications.vue';

export default {
  name: 'ClientHeader',
  components: {
    ClientNotifications
  },
  props: {
    userName: {
      type: String,
      default: 'User'
    },
    userEmail: {
      type: String,
      default: '<EMAIL>'
    },
    userAvatar: {
      type: String,
      default: null
    },
    showUserDropdown: {
      type: Boolean,
      default: false
    },
    sidebarCollapsed: {
      type: Boolean,
      default: false
    },
    activeMenu: {
      type: String,
      default: 'dashboard'
    },
    showBreadcrumbs: {
      type: Boolean,
      default: true
    }
  },

  emits: [
    'sidebar-toggle',
    'user-dropdown-toggle',
    'menu-action',
    'logout',
    'error',
    'search',
    'notification-click'
  ],

  setup(props, { emit }) {
    // Reactive state
    const showSearch = ref(false);
    const showBannerDetails = ref(false);
    const searchQuery = ref('');
    const searchInput = ref(null);

    // Computed properties
    const getPageTitle = computed(() => {
      const titles = {
        dashboard: 'Dashboard',
        services: 'Services',
        requests: 'My Requests',
        profile: 'My Profile',
        settings: 'Settings',
        help: 'Help & Support',
        documents: 'My Documents',
        history: 'Request History'
      };
      return titles[props.activeMenu] || 'Dashboard';
    });

    // Methods
    const toggleSearch = async () => {
      showSearch.value = !showSearch.value;

      if (showSearch.value) {
        await nextTick();
        searchInput.value?.focus();
      }
    };

    const closeSearch = () => {
      showSearch.value = false;
      searchQuery.value = '';
    };

    const toggleBannerDetails = () => {
      showBannerDetails.value = !showBannerDetails.value;
    };

    const handleSearch = () => {
      if (searchQuery.value.trim()) {
        emit('search', searchQuery.value.trim());
        closeSearch();
      }
    };

    const performSearch = () => {
      handleSearch();
    };

    const handleSidebarToggle = () => {
      emit('sidebar-toggle');
    };

    const handleUserDropdownToggle = () => {
      emit('user-dropdown-toggle');
    };

    const handleMenuAction = (action) => {
      emit('menu-action', action);
    };

    const handleLogout = () => {
      emit('logout');
    };

    const handleOutsideClick = (event) => {
      // Check if click is outside user dropdown
      if (!event.target.closest('.user-profile')) {
        if (props.showUserDropdown) {
          emit('user-dropdown-toggle');
        }
      }

      // Check if click is outside search
      if (!event.target.closest('.search-container')) {
        showSearch.value = false;
      }

      // Check if click is outside banner
      if (!event.target.closest('.gov-banner')) {
        showBannerDetails.value = false;
      }
    };

    // Notification event handlers
    const handleNewNotification = (notification) => {
      console.log('New notification received:', notification);
      // Handle new notification - could show toast, update UI, etc.
    };

    const handleNotificationClick = (notification) => {
      console.log('📊 ClientHeader: Handling notification click:', notification);

      try {
        // Parse notification data
        const notificationData = typeof notification.data === 'string'
          ? JSON.parse(notification.data)
          : notification.data || {};

        // Log navigation for debugging
        console.log('📊 ClientHeader: Notification data:', notificationData);

        // Additional header-specific logic can go here
        // For example, updating header state, showing badges, etc.

        // The navigation is now handled by the ClientNotifications component
        // This handler can focus on header-specific updates

      } catch (error) {
        console.error('❌ ClientHeader: Error handling notification click:', error);
      }

      // Always emit the event for other components that might need it
      emit('notification-click', notification);
    };

    const handleNotificationError = (error) => {
      console.error('Notification error:', error);
      emit('error', error);
    };

    // Lifecycle hooks
    onMounted(() => {
      document.addEventListener('click', handleOutsideClick);
    });

    onBeforeUnmount(() => {
      document.removeEventListener('click', handleOutsideClick);
    });

    return {
      // Reactive state
      showSearch,
      showBannerDetails,
      searchQuery,
      searchInput,

      // Computed
      getPageTitle,

      // Methods
      toggleSearch,
      closeSearch,
      toggleBannerDetails,
      handleSearch,
      performSearch,
      handleSidebarToggle,
      handleUserDropdownToggle,
      handleMenuAction,
      handleLogout,
      handleOutsideClick,
      handleNewNotification,
      handleNotificationClick,
      handleNotificationError
    };
  }
};
</script>

<style scoped src="./css/clientHeader.css"></style>
