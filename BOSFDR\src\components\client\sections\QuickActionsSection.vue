<template>
  <section class="quick-actions-section" aria-labelledby="quick-actions-title">
    <div class="container">
      <div class="section-header">
        <h2 id="quick-actions-title" class="section-title">Quick Actions</h2>
        <p class="section-description">Common tasks and frequently used services</p>
      </div>

      <div class="quick-actions-grid">
        <button 
          class="action-card primary" 
          @click="$emit('start-new-request')"
          type="button"
          aria-describedby="new-request-desc"
        >
          <div class="action-icon">
            <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
            </svg>
          </div>
          <div class="action-content">
            <h3>New Document Request</h3>
            <p id="new-request-desc">Start a new request for official documents</p>
          </div>
          <div class="action-arrow">
            <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"/>
            </svg>
          </div>
        </button>

        <button 
          class="action-card" 
          @click="$emit('view-requests')"
          type="button"
          aria-describedby="view-requests-desc"
        >
          <div class="action-icon">
            <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3,5H9V11H3V5M5,7V9H7V7H5M11,7H21V9H11V7M11,15H21V17H11V15M5,20L1.5,16.5L2.91,15.09L5,17.17L9.59,12.59L11,14L5,20Z"/>
            </svg>
          </div>
          <div class="action-content">
            <h3>My Requests</h3>
            <p id="view-requests-desc">Track and manage your document requests</p>
          </div>
          <div class="action-arrow">
            <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"/>
            </svg>
          </div>
        </button>

        <button 
          class="action-card" 
          @click="$emit('view-documents')"
          type="button"
          aria-describedby="view-documents-desc"
        >
          <div class="action-icon">
            <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M13,9H18.5L13,3.5V9M6,2H14L20,8V20A2,2 0 0,1 18,22H6C4.89,22 4,21.1 4,20V4C4,2.89 4.89,2 6,2M15,18V16H6V18H15M18,14V12H6V14H18Z"/>
            </svg>
          </div>
          <div class="action-content">
            <h3>My Documents</h3>
            <p id="view-documents-desc">Access your completed documents</p>
          </div>
          <div class="action-arrow">
            <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"/>
            </svg>
          </div>
        </button>

        <button 
          class="action-card" 
          @click="$emit('get-help')"
          type="button"
          aria-describedby="get-help-desc"
        >
          <div class="action-icon">
            <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C7.59,8 4,8 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z"/>
            </svg>
          </div>
          <div class="action-content">
            <h3>Help & Support</h3>
            <p id="get-help-desc">Get assistance with your requests</p>
          </div>
          <div class="action-arrow">
            <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"/>
            </svg>
          </div>
        </button>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'QuickActionsSection',
  emits: [
    'start-new-request',
    'view-requests',
    'view-documents',
    'get-help'
  ]
};
</script>

<style scoped>
/* Quick Actions Section */
.quick-actions-section {
  padding: var(--spacing-10) 0;
  background: var(--color-bg-secondary);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-10);
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-3);
  line-height: var(--line-height-3);
}

.section-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-5);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
}

.action-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-6);
  background: var(--color-bg-primary);
  border: 2px solid var(--color-border-light);
  border-radius: var(--radius-xl);
  text-decoration: none;
  color: var(--color-text-primary);
  transition: all var(--duration-base) var(--easing-standard);
  cursor: pointer;
  min-height: 80px;
  width: 100%;
  text-align: left;
}

.action-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px);
  box-shadow: var(--shadow-4);
}

.action-card:focus {
  outline: var(--focus-ring-width) solid var(--color-accent);
  outline-offset: var(--focus-ring-offset);
  border-color: var(--color-primary);
}

.action-card.primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: var(--color-text-inverse);
  border-color: var(--color-primary);
}

.action-card.primary:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-5);
}

.action-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
}

.action-card:not(.primary) .action-icon {
  background: var(--color-primary-lighter);
  color: var(--color-primary);
}

.action-icon svg {
  width: 24px;
  height: 24px;
}

.action-content {
  flex: 1;
  min-width: 0;
}

.action-content h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0 0 var(--spacing-1);
  line-height: var(--line-height-3);
}

.action-content p {
  font-size: var(--font-size-sm);
  margin: 0;
  opacity: 0.8;
  line-height: var(--line-height-4);
}

.action-arrow {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  opacity: 0.6;
  transition: all var(--duration-base) var(--easing-standard);
}

.action-card:hover .action-arrow {
  opacity: 1;
  transform: translateX(4px);
}

.action-arrow svg {
  width: 100%;
  height: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .action-card {
    padding: var(--spacing-5);
  }
  
  .action-icon {
    width: 40px;
    height: 40px;
  }
  
  .action-icon svg {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 480px) {
  .action-card {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-6);
  }
  
  .action-arrow {
    transform: rotate(90deg);
  }
  
  .action-card:hover .action-arrow {
    transform: rotate(90deg) translateX(4px);
  }
}
</style>
