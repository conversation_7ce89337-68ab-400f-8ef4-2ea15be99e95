<template>
  <section class="services-section" aria-labelledby="services-title" ref="servicesSection">
    <div class="container">
      <div class="section-header">
        <h2 id="services-title" class="section-title">Available Document Services</h2>
        <p class="section-description">Select the type of document you need to request</p>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="loading-container" role="status" aria-live="polite">
        <div class="loading-spinner">
          <svg class="spinner" aria-hidden="true" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
              <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
              <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
            </circle>
          </svg>
        </div>
        <p>Loading available services...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container" role="alert">
        <div class="error-content">
          <svg class="error-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
          </svg>
          <h3>Unable to Load Services</h3>
          <p>{{ error }}</p>
          <button class="btn btn-primary retry-btn" @click="$emit('retry')" type="button">
            <svg class="btn-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
            </svg>
            Try Again
          </button>
        </div>
      </div>

      <!-- Document Types Grid -->
      <div v-else class="document-types-grid">
        <button
          v-for="documentType in documentTypes"
          :key="documentType.id"
          class="document-card"
          @click="selectDocumentType(documentType)"
          :class="{ 'disabled': !documentType.is_active }"
          :disabled="!documentType.is_active"
          :aria-describedby="`doc-${documentType.id}-desc`"
          type="button"
        >
          <div class="document-header">
            <div class="document-icon">
              <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                <path :d="getDocumentIconPath(documentType.type_name)"/>
              </svg>
            </div>
            <div class="document-status">
              <span 
                v-if="!documentType.is_active" 
                class="status-badge unavailable"
                aria-label="Service unavailable"
              >
                Unavailable
              </span>
              <span 
                v-else 
                class="status-badge available"
                aria-label="Service available"
              >
                Available
              </span>
            </div>
          </div>

          <div class="document-content">
            <h3 class="document-title">{{ documentType.type_name }}</h3>
            <p :id="`doc-${documentType.id}-desc`" class="document-description">
              {{ documentType.description }}
            </p>

            <div class="document-details">
              <div class="detail-item">
                <svg class="detail-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
                </svg>
                <span class="detail-label">Fee:</span>
                <span class="detail-value fee-amount">₱{{ formatCurrency(documentType.base_fee) }}</span>
              </div>

              <div class="detail-item">
                <svg class="detail-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                </svg>
                <span class="detail-label">Processing:</span>
                <span class="detail-value">{{ getProcessingTime(documentType.type_name) }}</span>
              </div>
            </div>
          </div>

          <div class="document-action" v-if="documentType.is_active">
            <svg aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"/>
            </svg>
          </div>
        </button>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'DocumentServicesSection',
  props: {
    documentTypes: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: null
    }
  },
  emits: [
    'select-document-type',
    'retry'
  ],
  methods: {
    selectDocumentType(documentType) {
      if (documentType.is_active) {
        this.$emit('select-document-type', documentType);
      }
    },
    
    formatCurrency(amount) {
      return new Intl.NumberFormat('en-PH', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount || 0);
    },
    
    getProcessingTime(typeName) {
      const processingTimes = {
        'Barangay Clearance': '1-2 business days',
        'Certificate of Residency': '1-2 business days',
        'Certificate of Indigency': '2-3 business days',
        'Business Permit': '3-5 business days',
        'Barangay ID': '5-7 business days',
        'Certificate of Good Moral': '1-2 business days'
      };
      return processingTimes[typeName] || '3-5 business days';
    },
    
    getDocumentIconPath(typeName) {
      const iconPaths = {
        'Barangay Clearance': 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z',
        'Certificate of Residency': 'M12,3L2,12H5V20H19V12H22L12,3M12,8.75A2.25,2.25 0 0,1 14.25,11A2.25,2.25 0 0,1 12,13.25A2.25,2.25 0 0,1 9.75,11A2.25,2.25 0 0,1 12,8.75Z',
        'Certificate of Indigency': 'M17,18C15.89,18 15,18.89 15,20A3,3 0 0,0 18,23A3,3 0 0,0 21,20C21,18.89 20.1,18 19,18H17M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C7.59,22 4,18.41 4,14C4,9.59 7.59,6 12,6Z',
        'Business Permit': 'M20,6C20.58,6 21.05,6.2 21.42,6.59C21.8,7 22,7.45 22,8V19C22,19.55 21.8,20 21.42,20.41C21.05,20.8 20.58,21 20,21H4C3.42,21 2.95,20.8 2.58,20.41C2.2,20 2,19.55 2,19V8C2,7.45 2.2,7 2.58,6.59C2.95,6.2 3.42,6 4,6H8V4C8,3.42 8.2,2.95 8.58,2.58C8.95,2.2 9.42,2 10,2H14C14.58,2 15.05,2.2 15.42,2.58C15.8,2.95 16,3.42 16,4V6H20M4,8V19H20V8H4M10,4V6H14V4H10Z',
        'Barangay ID': 'M2,3H22C23.05,3 24,3.95 24,5V19C24,20.05 23.05,21 22,21H2C0.95,21 0,20.05 0,19V5C0,3.95 0.95,3 2,3M14,6V7H22V6H14M14,8V9H21.5L22,9V8H14M14,10V11H21V10H14M8,13.91C6,13.91 2,15 2,17V18H14V17C14,15 10,13.91 8,13.91M8,6A3,3 0 0,0 5,9A3,3 0 0,0 8,12A3,3 0 0,0 11,9A3,3 0 0,0 8,6Z',
        'Certificate of Good Moral': 'M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z'
      };
      return iconPaths[typeName] || iconPaths['Barangay Clearance'];
    }
  }
};
</script>

<style scoped>
/* Document Services Section */
.services-section {
  padding: var(--spacing-10) 0;
  background: var(--color-bg-primary);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-10);
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-3);
  line-height: var(--line-height-3);
}

.section-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-5);
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-12) var(--spacing-4);
  color: var(--color-text-secondary);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  margin-bottom: var(--spacing-4);
  color: var(--color-primary);
}

.spinner {
  width: 100%;
  height: 100%;
}

/* Error State */
.error-container {
  display: flex;
  justify-content: center;
  padding: var(--spacing-12) var(--spacing-4);
}

.error-content {
  text-align: center;
  max-width: 400px;
}

.error-icon {
  width: 48px;
  height: 48px;
  color: var(--color-error);
  margin-bottom: var(--spacing-4);
}

.error-content h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-3);
}

.error-content p {
  color: var(--color-text-secondary);
  margin: 0 0 var(--spacing-6);
  line-height: var(--line-height-5);
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-md);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-base) var(--easing-standard);
  min-height: 48px;
}

.btn-primary {
  background: var(--color-primary);
  color: var(--color-text-inverse);
  border-color: var(--color-primary);
}

.btn-primary:hover,
.btn-primary:focus {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-3);
}

.btn:focus {
  outline: var(--focus-ring-width) solid var(--color-accent);
  outline-offset: var(--focus-ring-offset);
}

.btn-icon {
  width: 20px;
  height: 20px;
}

/* Document Types Grid */
.document-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-6);
}

.document-card {
  display: flex;
  flex-direction: column;
  padding: var(--spacing-6);
  background: var(--color-bg-primary);
  border: 2px solid var(--color-border-light);
  border-radius: var(--radius-xl);
  transition: all var(--duration-base) var(--easing-standard);
  cursor: pointer;
  text-align: left;
  width: 100%;
}

.document-card:hover:not(.disabled) {
  border-color: var(--color-primary);
  transform: translateY(-4px);
  box-shadow: var(--shadow-4);
}

.document-card:focus:not(.disabled) {
  outline: var(--focus-ring-width) solid var(--color-accent);
  outline-offset: var(--focus-ring-offset);
  border-color: var(--color-primary);
}

.document-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--color-bg-secondary);
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.document-icon {
  width: 48px;
  height: 48px;
  background: var(--color-primary-lighter);
  color: var(--color-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.document-icon svg {
  width: 24px;
  height: 24px;
}

.status-badge {
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.available {
  background: var(--color-success);
  color: var(--color-text-inverse);
}

.status-badge.unavailable {
  background: var(--color-error);
  color: var(--color-text-inverse);
}

.document-content {
  flex: 1;
  margin-bottom: var(--spacing-4);
}

.document-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-2);
  line-height: var(--line-height-3);
}

.document-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0 0 var(--spacing-4);
  line-height: var(--line-height-5);
}

.document-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.detail-icon {
  width: 16px;
  height: 16px;
  color: var(--color-text-tertiary);
}

.detail-label {
  color: var(--color-text-secondary);
  font-weight: 500;
}

.detail-value {
  color: var(--color-text-primary);
  font-weight: 600;
}

.fee-amount {
  color: var(--color-primary);
}

.document-action {
  align-self: flex-end;
  width: 24px;
  height: 24px;
  color: var(--color-text-tertiary);
  transition: all var(--duration-base) var(--easing-standard);
}

.document-card:hover:not(.disabled) .document-action {
  color: var(--color-primary);
  transform: translateX(4px);
}

.document-action svg {
  width: 100%;
  height: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .document-types-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .document-card {
    padding: var(--spacing-5);
  }
}
</style>
