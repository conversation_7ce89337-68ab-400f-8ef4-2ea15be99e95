<template>
  <section class="info-section" aria-labelledby="info-title">
    <div class="container">
      <div class="info-grid">
        <!-- Requirements Card -->
        <div class="info-card requirements-card">
          <div class="card-header">
            <svg class="card-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19M17,12H7V10H17V12M15,16H7V14H15V16M17,8H7V6H17V8Z"/>
            </svg>
            <h3>Before You Start</h3>
          </div>
          <div class="card-content">
            <ul class="requirements-list" role="list">
              <li class="requirement-item">
                <svg class="check-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
                </svg>
                <span>Complete and accurate profile information</span>
              </li>
              <li class="requirement-item">
                <svg class="check-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
                </svg>
                <span>Valid government-issued ID ready for upload</span>
              </li>
              <li class="requirement-item">
                <svg class="check-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
                </svg>
                <span>Supporting documents (if required)</span>
              </li>
              <li class="requirement-item">
                <svg class="check-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
                </svg>
                <span>Payment method for processing fees</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- Process Card -->
        <div class="info-card process-card">
          <div class="card-header">
            <svg class="card-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M14,2A8,8 0 0,1 22,10A8,8 0 0,1 14,18A8,8 0 0,1 6,10A8,8 0 0,1 14,2M14,4A6,6 0 0,0 8,10A6,6 0 0,0 14,16A6,6 0 0,0 20,10A6,6 0 0,0 14,4M14,5.5A4.5,4.5 0 0,1 18.5,10A4.5,4.5 0 0,1 14,14.5A4.5,4.5 0 0,1 9.5,10A4.5,4.5 0 0,1 14,5.5M14,7A3,3 0 0,0 11,10A3,3 0 0,0 14,13A3,3 0 0,0 17,10A3,3 0 0,0 14,7M2,18A2,2 0 0,1 4,16H6A2,2 0 0,1 8,18V20A2,2 0 0,1 6,22H4A2,2 0 0,1 2,20V18Z"/>
            </svg>
            <h3>How It Works</h3>
          </div>
          <div class="card-content">
            <ol class="process-steps" role="list">
              <li class="process-step">
                <div class="step-number" aria-hidden="true">1</div>
                <div class="step-content">
                  <strong>Select Document</strong>
                  <span>Choose the document type you need</span>
                </div>
              </li>
              <li class="process-step">
                <div class="step-number" aria-hidden="true">2</div>
                <div class="step-content">
                  <strong>Fill Application</strong>
                  <span>Complete the required information</span>
                </div>
              </li>
              <li class="process-step">
                <div class="step-number" aria-hidden="true">3</div>
                <div class="step-content">
                  <strong>Submit & Pay</strong>
                  <span>Review and submit with payment</span>
                </div>
              </li>
              <li class="process-step">
                <div class="step-number" aria-hidden="true">4</div>
                <div class="step-content">
                  <strong>Track Progress</strong>
                  <span>Monitor your request status</span>
                </div>
              </li>
            </ol>
          </div>
        </div>

        <!-- Help Card -->
        <div class="info-card help-card">
          <div class="card-header">
            <svg class="card-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
            </svg>
            <h3>Need Assistance?</h3>
          </div>
          <div class="card-content">
            <p class="help-description">
              Our support team is here to help you with any questions about document requirements or the application process.
            </p>
            <div class="help-actions">
              <button 
                class="btn btn-outline help-btn" 
                @click="$emit('open-help')"
                type="button"
              >
                <svg class="btn-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C7.59,8 4,8 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z"/>
                </svg>
                View FAQ
              </button>
              <button 
                class="btn btn-outline contact-btn" 
                @click="$emit('contact-support')"
                type="button"
              >
                <svg class="btn-icon" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"/>
                </svg>
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'InformationSection',
  emits: [
    'open-help',
    'contact-support'
  ]
};
</script>

<style scoped>
/* Information Section */
.info-section {
  padding: var(--spacing-10) 0;
  background: var(--color-bg-secondary);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-6);
}

.info-card {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  transition: all var(--duration-base) var(--easing-standard);
}

.info-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-4);
  border-color: var(--color-primary-light);
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-5);
}

.card-icon {
  width: 32px;
  height: 32px;
  color: var(--color-primary);
}

.card-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
  line-height: var(--line-height-3);
}

.card-content {
  color: var(--color-text-secondary);
  line-height: var(--line-height-5);
}

/* Requirements List */
.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirement-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-sm);
}

.requirement-item:last-child {
  margin-bottom: 0;
}

.check-icon {
  width: 20px;
  height: 20px;
  color: var(--color-success);
  flex-shrink: 0;
  margin-top: 2px;
}

/* Process Steps */
.process-steps {
  list-style: none;
  padding: 0;
  margin: 0;
}

.process-step {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-5);
  position: relative;
}

.process-step:last-child {
  margin-bottom: 0;
}

.process-step:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 18px;
  top: 36px;
  bottom: -20px;
  width: 2px;
  background: var(--color-border-medium);
}

.step-number {
  width: 36px;
  height: 36px;
  background: var(--color-primary);
  color: var(--color-text-inverse);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: var(--font-size-sm);
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.step-content {
  flex: 1;
  padding-top: var(--spacing-1);
}

.step-content strong {
  display: block;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
  line-height: var(--line-height-3);
}

.step-content span {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-4);
}

/* Help Section */
.help-description {
  font-size: var(--font-size-sm);
  margin: 0 0 var(--spacing-5);
  line-height: var(--line-height-5);
}

.help-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  border: 2px solid var(--color-border-medium);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-base) var(--easing-standard);
  min-height: 44px;
  background: transparent;
  color: var(--color-text-primary);
  width: 100%;
}

.btn:hover,
.btn:focus {
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-2);
}

.btn:focus {
  outline: var(--focus-ring-width) solid var(--color-accent);
  outline-offset: var(--focus-ring-offset);
}

.btn-icon {
  width: 18px;
  height: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-5);
  }
  
  .info-card {
    padding: var(--spacing-5);
  }
  
  .help-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }
  
  .process-step {
    gap: var(--spacing-3);
  }
  
  .step-number {
    width: 32px;
    height: 32px;
  }
  
  .process-step:not(:last-child)::after {
    left: 15px;
  }
}
</style>
